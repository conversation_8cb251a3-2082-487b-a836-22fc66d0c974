import logging
import hashlib

from datetime import datetime
from typing import List, Dict, Any, Optional

from cataloging_service.domain import service_provider

from cataloging_service.domain.entities.properties.property_onboarding import (
    PropertyOnboardingEntity,
)
from cataloging_service.domain.entities.properties.property_profit_center import (
    PropertyProfitCenterEntity,
)
from cataloging_service.domain.entities.properties.property_department import (
    PropertyDepartmentEntity,
)
from cataloging_service.domain.services.property.property_department_service import (
    PropertyDepartmentService,
)
from cataloging_service.domain.services.property.property_profit_center_service import (
    PropertyProfitCenterService,
)
from cataloging_service.domain.services.property.property_sku_service import (
    PropertySkuService,
)
from cataloging_service.domain.services.template.department_template_service import (
    DepartmentTemplateService,
)
from cataloging_service.domain.services.template.profit_center_template_service import (
    ProfitCenterTemplateService,
)
from cataloging_service.domain.services.template.sku_template_service import (
    SkuTemplateService,
)
from cataloging_service.domain.entities.properties.property_sku import (
    PropertySkuEntity,
)
from cataloging_service.domain.services.template.template_filter import TemplateFilter
from cataloging_service.schemas.transaction_schemas import (
    PropertyOnboardingRequestSchema,
    PropertyOnboardingResponseSchema,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        DepartmentTemplateService,
        ProfitCenterTemplateService,
        SkuTemplateService,
        PropertyDepartmentService,
        PropertyProfitCenterService,
        PropertySkuService,
    ]
)
class PropertyOnboardingService:
    def __init__(
        self,
        department_template_service,
        profit_center_template_service,
        sku_template_service,
        property_department_service,
        property_profit_center_service,
        property_sku_service,
    ):
        self.department_template_service = department_template_service
        self.profit_center_template_service = profit_center_template_service
        self.sku_template_service = sku_template_service
        self.property_department_service = property_department_service
        self.property_profit_center_service = property_profit_center_service
        self.property_sku_service = property_sku_service
        self.property_service = service_provider.property_service

    def onboard_property(
        self, request: PropertyOnboardingRequestSchema
    ) -> PropertyOnboardingResponseSchema:
        onboarding_entity = PropertyOnboardingEntity(
            property_id=request.property_id,
            brand_id=request.brand_id,
            template_filters=request.custom_config,
        )

        try:
            # Step 1: Validate property
            self._validate_property(request.property_id)

            # Step 2: Create departments from templates
            if request.auto_create_departments:
                departments_created = self._create_departments_from_templates(
                    onboarding_entity, request.custom_config
                )
                onboarding_entity.departments_created = len(departments_created)

            # Step 3: Create profit centers from templates
            if request.auto_create_profit_centers:
                profit_centers_created = self._create_profit_centers_from_templates(
                    onboarding_entity, request.custom_config
                )
                onboarding_entity.profit_centers_created = len(profit_centers_created)

            # Step 4: Create SKUs from templates
            if request.auto_create_skus:
                skus_created = self._create_skus_from_templates(
                    onboarding_entity, request.custom_config
                )
                onboarding_entity.skus_created = len(skus_created)

            # Step 5: Generate transaction codes
            if request.generate_transaction_codes:
                transaction_codes = self._generate_transaction_codes(onboarding_entity)
                onboarding_entity.transaction_codes_generated = len(transaction_codes)

            # Mark as completed
            onboarding_entity.mark_completed()

            # Return response
            return PropertyOnboardingResponseSchema(
                property_id=onboarding_entity.property_id,
                brand_id=onboarding_entity.brand_id,
                departments_created=onboarding_entity.departments_created,
                profit_centers_created=onboarding_entity.profit_centers_created,
                skus_created=onboarding_entity.skus_created,
                transaction_codes_generated=onboarding_entity.transaction_codes_generated,
                onboarding_status=onboarding_entity.onboarding_status,
                created_entities=onboarding_entity.created_entities,
                errors=onboarding_entity.errors,
                warnings=onboarding_entity.warnings,
                onboarded_at=onboarding_entity.onboarded_at or datetime.now(),
            )

        except Exception as e:
            onboarding_entity.add_error(f"Onboarding failed: {str(e)}")
            logger.exception(
                f"Property onboarding failed for property_id: {request.property_id}"
            )
            return PropertyOnboardingResponseSchema(
                property_id=onboarding_entity.property_id,
                brand_id=onboarding_entity.brand_id,
                departments_created=onboarding_entity.departments_created,
                profit_centers_created=onboarding_entity.profit_centers_created,
                skus_created=onboarding_entity.skus_created,
                transaction_codes_generated=onboarding_entity.transaction_codes_generated,
                onboarding_status=onboarding_entity.onboarding_status,
                created_entities=onboarding_entity.created_entities,
                errors=onboarding_entity.errors,
                warnings=onboarding_entity.warnings,
                onboarded_at=datetime.utcnow(),
            )

    def _validate_property(self, property_id: str) -> tuple:
        property_obj = self.property_service.get_property(property_id)
        if not property_obj:
            raise ValueError(f"Property with ID '{property_id}' not found")
        return property_obj

    def _create_departments_from_templates(
        self, onboarding_entity: PropertyOnboardingEntity, custom_config: Dict[str, Any]
    ) -> List[str]:
        created_departments = []
        try:
            template_filter = TemplateFilter(
                brand_id=onboarding_entity.brand_id,
                is_active=True,
                auto_create_on_property_launch=True,
            )
            department_templates = (
                self.department_template_service.get_department_templates(
                    filters=template_filter
                )
            )
            if not department_templates:
                onboarding_entity.add_warning(
                    "No department templates found for auto-creation"
                )
                return created_departments
            for template in department_templates:
                try:
                    department_entity = self._convert_department_template_to_entity(
                        template, onboarding_entity.property_id
                    )
                    created_department = (
                        self.property_department_service.create_property_department(
                            department_entity
                        )
                    )
                    created_departments.append(created_department.code)
                except Exception as e:
                    error_msg = f"Failed to create department from template {template.code}: {str(e)}"
                    onboarding_entity.add_error(error_msg)
                    logger.error(error_msg)
        except Exception as e:
            error_msg = f"Failed to create departments from templates: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)
        return created_departments

    def _create_profit_centers_from_templates(
        self, onboarding_entity: PropertyOnboardingEntity, custom_config: Dict[str, Any]
    ) -> List[str]:
        created_profit_centers = []
        try:
            template_filter = TemplateFilter(
                brand_id=onboarding_entity.brand_id,
                is_active=True,
                auto_create_on_property_launch=True,
            )
            profit_center_templates = (
                self.profit_center_template_service.get_profit_center_templates(
                    template_filter
                )
            )

            if not profit_center_templates:
                onboarding_entity.add_warning(
                    "No profit center templates found for auto-creation"
                )
                return created_profit_centers
            for template in profit_center_templates:
                try:
                    profit_center_entity = (
                        self._convert_profit_center_template_to_entity(
                            template, onboarding_entity.property_id
                        )
                    )
                    created_profit_center = self.property_profit_center_service.create_property_profit_center(
                        entity=profit_center_entity
                    )
                    created_profit_centers.append(created_profit_center.code)

                except Exception as e:
                    error_msg = f"Failed to create profit center from template {template.code}: {str(e)}"
                    onboarding_entity.add_error(error_msg)
                    logger.error(error_msg)

        except Exception as e:
            error_msg = f"Failed to create profit centers from templates: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)

        return created_profit_centers

    def _create_skus_from_templates(
        self, onboarding_entity: PropertyOnboardingEntity, custom_config: Dict[str, Any]
    ) -> List[str]:
        created_skus = []
        try:
            template_filter = TemplateFilter(
                is_active=True, auto_create_on_property_launch=True
            )

            sku_templates = self.sku_template_service.get_sku_templates(template_filter)

            brand_sku_templates = []
            for template in sku_templates:
                dept_filter = TemplateFilter(
                    brand_id=onboarding_entity.brand_id,
                    code=template.department_template_code,
                    is_active=True,
                )
                dept_templates = (
                    self.department_template_service.get_department_templates(
                        dept_filter
                    )
                )
                if dept_templates:
                    brand_sku_templates.append(template)
            if not brand_sku_templates:
                onboarding_entity.add_warning(
                    "No SKU templates found for auto-creation"
                )
                return created_skus
            for template in brand_sku_templates:
                try:
                    sku_entity = self._convert_sku_template_to_entity(
                        template, onboarding_entity.property_id
                    )
                    created_sku = self.property_sku_service.create_property_sku(
                        entity=sku_entity
                    )
                    created_skus.append(created_sku.code)

                except Exception as e:
                    error_msg = (
                        f"Failed to create SKU from template {template.code}: {str(e)}"
                    )
                    onboarding_entity.add_error(error_msg)
                    logger.error(error_msg)

        except Exception as e:
            error_msg = f"Failed to create SKUs from templates: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)

        return created_skus

    def _generate_transaction_codes(
        self, onboarding_entity: PropertyOnboardingEntity
    ) -> List[str]:
        transaction_codes = []
        try:
            # Generate codes for departments
            for dept_code in onboarding_entity.created_entities.get("departments", []):
                tx_code = f"TXN_{onboarding_entity.property_id}_{dept_code}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
                transaction_codes.append(tx_code)

            # Generate codes for profit centers
            for pc_code in onboarding_entity.created_entities.get("profit_centers", []):
                tx_code = f"TXN_{onboarding_entity.property_id}_{pc_code}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
                transaction_codes.append(tx_code)

            # Generate codes for SKUs
            for sku_code in onboarding_entity.created_entities.get("skus", []):
                tx_code = f"TXN_{onboarding_entity.property_id}_{sku_code}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
                transaction_codes.append(tx_code)

            if not transaction_codes:
                onboarding_entity.add_warning(
                    "No transaction codes generated - no entities were created"
                )

        except Exception as e:
            error_msg = f"Failed to generate transaction codes: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)

        return transaction_codes

    def _convert_department_template_to_entity(self, template, property_id: str):
        return PropertyDepartmentEntity(
            property_id=property_id,
            code=template.code,
            name=template.name,
            parent_id=None,
            description=template.description,
            financial_code=template.financial_code,
            is_active=template.is_active,
            is_custom=False,
            created_from_template_code=template.code,
        )

    def _convert_profit_center_template_to_entity(self, template, property_id: str):
        profit_center_id = self._generate_profit_center_id(property_id, template.code)
        department_id = self._resolve_department_id_from_template_code(
            property_id, template.department_template_code
        )
        if not department_id:
            raise ValueError(
                f"Could not resolve department_id for template code '{template.department_template_code}' "
                f"in property '{property_id}'. Ensure the department was created first."
            )

        return PropertyProfitCenterEntity(
            id=profit_center_id,
            property_id=property_id,
            code=template.code,
            name=template.name,
            description=template.description,
            department_id=department_id,
            is_active=template.is_active,
            is_auto_created=True,
            is_custom=False,
            created_from_template_code=template.code,
            config=template.template_config or {},
        )

    def _convert_sku_template_to_entity(self, template, property_id: str):
        department_id = self._resolve_department_id_from_template_code(
            property_id, template.department_template_code
        )
        profit_center_id = self._resolve_profit_center_id_from_template_code(
            property_id, template.profit_center_template_code
        )
        return PropertySkuEntity(
            property_id=property_id,
            code=template.code,
            name=template.name,
            display_name=template.display_name,
            description=template.description,
            department_id=department_id,
            profit_center_id=profit_center_id,
            sku_category_id=template.category_id,
            default_list_price=template.default_list_price,
            default_sale_price=template.default_sale_price,
            hsn_sac=template.hsn_sac,
            tax_at_room_rate=template.tax_at_room_rate,
            is_active=template.is_active,
            is_custom=False,
            is_modular=template.is_modular,
            saleable=template.saleable,
            chargeable_per_occupant=template.chargeable_per_occupant,
            is_property_inclusion=template.is_property_inclusion,
            created_from_template_code=template.code,
            config=template.template_config or {},
            sku_details=template.sku_details or {},
            sku_count=0,
            flat_count_for_creation=0,
            offering={},
            frequency={},
        )

    def _resolve_department_id_from_template_code(
        self, property_id: str, template_code: str
    ) -> Optional[int]:
        try:
            departments = self.property_department_service.get_property_departments(
                property_id, active_only=False
            )
            for dept in departments:
                if dept.created_from_template_code == template_code:
                    return dept.id
            logger.warning(
                f"Department with template code '{template_code}' not found for property {property_id}. "
                f"Available departments: {[d.created_from_template_code for d in departments if d.created_from_template_code]}"
            )
            return None

        except Exception as e:
            logger.error(
                f"Failed to resolve department ID for template code {template_code}: {e}"
            )
            return None

    def _resolve_profit_center_id_from_template_code(
        self, property_id: str, template_code: str
    ) -> Optional[str]:
        try:
            profit_centers = (
                self.property_profit_center_service.get_property_profit_centers(
                    property_id, active_only=False
                )
            )
            for pc in profit_centers:
                if pc.created_from_template_code == template_code:
                    return pc.id
            raise ValueError(
                f"Profit center with template code '{template_code}' not found for property {property_id}"
            )

        except Exception as e:
            logger.error(
                f"Failed to resolve profit center ID for template code {template_code}: {e}"
            )
            return None

    def _generate_profit_center_id(self, property_id: str, code: str) -> str:
        base_string = f"{property_id}_{code}_{datetime.now().isoformat()}"
        hash_object = hashlib.md5(base_string.encode())
        short_hash = hash_object.hexdigest()[:8].upper()
        profit_center_id = f"PC_{property_id}_{short_hash}"
        return profit_center_id
